import React, { useState } from 'react'
import { MentionInput } from './mention-input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

/**
 * Demo component to test the mention input bug fix
 * This component can be temporarily added to a page to manually test the fix
 */
export const MentionInputDemo = () => {
  const [value, setValue] = useState('')

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Mention Input Bug Fix Demo</CardTitle>
        <CardDescription>
          Test the fix for the mention dropdown bug:
          <br />
          1. Type @ and select a participant (mention token + space will be inserted)
          <br />
          2. Delete the space after the mention token
          <br />
          3. The dropdown should NOT appear (this was the bug)
          <br />
          4. Type @ again to start a new mention - dropdown should appear normally
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Test Input:</label>
            <MentionInput
              hubId={1} // Use a valid hub ID from your test data
              value={value}
              onChange={setValue}
              placeholder="Type @ to mention someone..."
            >
              {(props) => (
                <div
                  {...props}
                  className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              )}
            </MentionInput>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Raw Value (email-based):</label>
            <pre className="text-xs bg-muted p-2 rounded border overflow-auto">
              {value || '(empty)'}
            </pre>
          </div>
          
          <div className="text-xs text-muted-foreground">
            <strong>Expected behavior:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Typing @ followed by characters should show participant dropdown</li>
              <li>Selecting a participant creates a mention token with trailing space</li>
              <li>Deleting the space after a mention token should NOT trigger dropdown</li>
              <li>Cursor positioned right after mention token should NOT trigger dropdown</li>
              <li>Only typing @ in empty space should trigger new mention dropdown</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
